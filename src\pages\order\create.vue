<template>
  <div class="order-create">
    <!-- 顶部导航 -->
    <van-nav-bar
      :title="isEdit ? '修改订单' : '创建订单'"
      left-arrow
      @click-left="onBack"
      fixed
      class="order-nav"
    >
      <template #right>
        <van-icon name="down" @click="showImportAction" v-if="!isEdit" />
      </template>
    </van-nav-bar>

    <!-- 主体内容区 -->
    <div class="order-container">
      <!-- 收货信息 - 位置调换到前面 -->
      <div class="order-section">
        <div class="section-header">
          <span class="section-title">收货信息</span>
        </div>

        <van-cell-group inset>
          <!-- 管理员专属：选择经销商 -->
          <template v-if="isAdmin">
            <van-field
              v-model="selectedDealer"
              label="经销商账号"
              placeholder="请选择经销商账号"
              is-link
              readonly
              @click="showDealerPicker = true"
              required
            />
            <van-field
              v-model="selectedCompany"
              label="公司名称"
              readonly
              placeholder="自动获取"
            />
          </template>

          <van-field v-model="orderNo" label="订单号" readonly v-if="isEdit" />

          <van-field v-model="createDate" label="创建日期" readonly />

          <!-- 收货信息表单 -->
          <van-field
            v-model="shipping.name"
            label="收货人"
            placeholder="请输入收货人姓名"
            required
          />
          <van-field
            v-model="shipping.phone"
            label="联系电话"
            placeholder="请输入联系电话"
            required
          />
          <van-field
            v-model="shipping.email"
            label="电子邮箱"
            placeholder="请输入电子邮箱"
            type="email"
          />
          <van-field
            v-model="shipping.address"
            label="收货地址"
            type="textarea"
            placeholder="请输入详细地址"
            rows="2"
            autosize
            required
          />
          <van-field
            v-model="billing.address"
            label="账单地址"
            type="textarea"
            placeholder="请输入账单地址(选填)"
            rows="2"
            autosize
          />
          <van-field
            v-model="deliveryDate"
            label="交货日期"
            placeholder="请选择交货日期"
            is-link
            readonly
            @click="showDeliveryPicker = true"
            required
          />
          <van-field
            v-model="paymentDate"
            label="付款日期"
            placeholder="请选择付款日期"
            is-link
            readonly
            @click="showPaymentPicker = true"
            required
          />
          <van-field
            v-model="orderRemark"
            label="订单备注"
            type="textarea"
            placeholder="请输入订单备注信息(选填)"
            rows="2"
            autosize
          />
        </van-cell-group>
      </div>

      <!-- 商品列表 - 位置调换到后面 -->
      <div class="order-section">
        <div class="section-header">
          <span class="section-title">商品信息</span>
          <van-button
            type="primary"
            size="small"
            icon="plus"
            @click="addProduct"
            >添加商品</van-button
          >
        </div>

        <van-empty
          v-if="products.length === 0"
          description="暂无商品，请添加商品"
        />

        <div v-else class="product-list">
          <!-- 商品卡片 -->
          <van-swipe-cell
            v-for="(product, index) in products"
            :key="index"
            class="product-item"
          >
            <van-cell-group :border="false" inset>
              <div class="product-card">
                <div class="product-header" @click="toggleProduct(index)">
                  <div class="product-title">商品 #{{ index + 1 }}</div>
                  <van-icon
                    :name="product.expanded ? 'arrow-up' : 'arrow-down'"
                    class="toggle-icon"
                  />
                </div>

                <div v-show="product.expanded" class="product-content">
                  <van-field
                    v-model="product.code"
                    label="型号/编号"
                    placeholder="请输入型号或编号"
                    required
                  />
                  <van-field
                    v-model="product.name"
                    label="商品名称"
                    placeholder="请输入商品名称"
                    required
                  />
                  <div class="price-quantity">
                    <van-field
                      v-model="product.quantity"
                      label="数量"
                      type="digit"
                      placeholder="请输入数量"
                      required
                    />
                    <van-field
                      v-model="product.price"
                      label="单价"
                      type="digit"
                      placeholder="请输入单价"
                      required
                    >
                      <template #prefix>
                        {{ currencySymbol }}
                      </template>
                    </van-field>
                  </div>

                  <van-field
                    v-model="product.remark"
                    label="备注"
                    type="textarea"
                    placeholder="请输入备注信息(选填)"
                    rows="2"
                    autosize
                  />

                  <div class="product-subtotal">
                    <span>小计：</span>
                    <span class="price"
                      >{{ currencySymbol
                      }}{{ formatPriceValue(calculateSubtotal(product)) }}</span
                    >
                  </div>
                </div>
              </div>
            </van-cell-group>

            <template #right>
              <van-button
                square
                text="删除"
                type="danger"
                class="delete-btn"
                @click="removeProduct(index)"
              />
            </template>
          </van-swipe-cell>
        </div>
      </div>

      <!-- 订单总计 -->
      <div class="order-total">
        <div class="total-label">订单总计</div>
        <div class="total-amount">
          {{ currencySymbol }}{{ formatPriceValue(calculateTotal()) }}
        </div>
      </div>

      <!-- 底部提交按钮 -->
      <div class="submit-bar">
        <van-button
          type="primary"
          block
          round
          @click="submitOrder"
          :loading="submitting"
        >
          {{ isEdit ? "保存订单" : "提交订单" }}
        </van-button>
      </div>
    </div>

    <!-- 弹出层 -->
    <!-- 经销商选择器 -->
    <van-popup v-model:show="showDealerPicker" position="bottom">
      <van-picker
        :columns="dealerOptions"
        @confirm="onDealerConfirm"
        @cancel="showDealerPicker = false"
        title="选择经销商账号"
        show-toolbar
      />
    </van-popup>

    <!-- 交货日期选择器 -->
    <van-popup v-model:show="showDeliveryPicker" position="bottom">
      <van-date-picker
        :min-date="minDate"
        v-model="selectedDeliveryDate"
        @confirm="onDeliveryDateConfirm"
        @cancel="showDeliveryPicker = false"
        title="选择交货日期"
      />
    </van-popup>

    <!-- 付款日期选择器 -->
    <van-popup v-model:show="showPaymentPicker" position="bottom">
      <van-date-picker
        :min-date="minDate"
        v-model="selectedPaymentDate"
        @confirm="onPaymentDateConfirm"
        @cancel="showPaymentPicker = false"
        title="选择付款日期"
      />
    </van-popup>

    <!-- Excel导入弹出菜单 -->
    <van-action-sheet v-model:show="showImportSheet" title="导入Excel">
      <div class="import-actions">
        <van-button icon="download" block @click="downloadTemplate"
          >下载导入模板</van-button
        >
        <van-uploader
          :max-count="1"
          :before-read="validateExcel"
          :after-read="handleExcelUpload"
          accept=".xlsx,.xls"
        >
          <van-button icon="plus" block>选择Excel文件</van-button>
        </van-uploader>
      </div>
    </van-action-sheet>
  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { showToast, showSuccessToast, showLoadingToast } from "vant";
import { useUserStore } from "@/stores/user.js";
import { useI18n } from "vue-i18n";
import apiService from "@/utils/api";

const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const userStore = useUserStore();
const userInfo = computed(() => {
  return (
    userStore.userInfo || {
      name: t("user.guest"),
      id: "--",
      loginName: "--",
    }
  );
});

// 状态变量
const isEdit = computed(() => !!route.query.id);
const orderId = ref(route.query.id || "");
const orderNo = ref("");
const createDate = ref(new Date().toISOString().substring(0, 10));
const submitting = ref(false);

// 用户信息
const isAdmin = computed(() => userInfo.value.userType == 1);
const currencySymbol = computed(() => {
  switch (userInfo.value.currency) {
    case "1":
      return "¥";
    case "2":
      return "$";
    case "3":
      return "€";
    default:
      return "¥";
  }
});

// 商品数据
const products = ref([]);

// 收货信息
const shipping = reactive({
  name: "",
  phone: "",
  email: "",
  address: "",
});

// 账单信息
const billing = reactive({
  address: "",
});

// 订单信息
const orderRemark = ref("");
const deliveryDate = ref("");
const paymentDate = ref("");

// 日期选择器
const minDate = new Date();
const showDeliveryPicker = ref(false);
const showPaymentPicker = ref(false);
const selectedDeliveryDate = ref(new Date());
const selectedPaymentDate = ref(new Date());

// 经销商数据
const selectedDealer = ref("");
const selectedCompany = ref("");
const showDealerPicker = ref(false);
const dealerList = ref([]);
const dealerOptions = computed(() => {
  return dealerList.value.map((item) => ({
    text: item.name,
    value: item.id,
  }));
});

// 添加额外的经销商信息对象
const dealerInfo = ref({
  dealerId: "",
  customerId: "",
});

// Excel导入
const showImportSheet = ref(false);

// 返回上一页
const onBack = () => {
  router.back();
};

// 添加商品
const addProduct = () => {
  products.value.push({
    code: "",
    name: "",
    quantity: 1,
    price: 0,
    remark: "",
    expanded: true,
  });
};

// 移除商品
const removeProduct = (index) => {
  products.value.splice(index, 1);
};

// 切换商品展开/收起
const toggleProduct = (index) => {
  products.value[index].expanded = !products.value[index].expanded;
};

// 计算单个商品小计
const calculateSubtotal = (product) => {
  const price = parseFloat(product.price) || 0;
  const quantity = parseFloat(product.quantity) || 0;
  return price * quantity;
};

// 计算订单总金额
const calculateTotal = () => {
  return products.value.reduce((total, product) => {
    return total + calculateSubtotal(product);
  }, 0);
};

// 日期格式化
const formatDate = (date) => {
  return date
    ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`
    : "";
};

// 交货日期确认
const onDeliveryDateConfirm = (value) => {
  selectedDeliveryDate.value = value;
  deliveryDate.value = formatDate(value);
  showDeliveryPicker.value = false;
};

// 付款日期确认
const onPaymentDateConfirm = (value) => {
  selectedPaymentDate.value = value;
  paymentDate.value = formatDate(value);
  showPaymentPicker.value = false;
};

// 经销商确认选择
const onDealerConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0];
  selectedDealer.value = option.text;

  // 查找经销商相关信息
  const dealer = dealerList.value.find((item) => item.id === option.value);

  if (dealer) {
    const companyInfo = dealer.companyInfo || {};

    selectedCompany.value = companyInfo.name || "";

    // 自动填充地址信息
    shipping.address = companyInfo.address || "";
    shipping.name = companyInfo.linkman || "";
    shipping.phone = companyInfo.telephone || "";
    shipping.email = companyInfo.email || "";

    // 设置经销商ID和客户ID
    dealerInfo.value = {
      dealerId: dealer.id,
      customerId: dealer.customerId || (companyInfo ? companyInfo.id : ""),
    };
    console.log("设置的经销商信息:", dealerInfo.value);
  }

  showDealerPicker.value = false;
};

// 显示导入Excel菜单
const showImportAction = () => {
  showImportSheet.value = true;
};

// 下载Excel模板
const downloadTemplate = () => {
  const a = document.createElement("a");
  const currentDomain = window.location.origin;
  const fullUrl =
    currentDomain + "/fileviewer/_Files/template/Order_Excel_Import.xlsx";
  a.href = fullUrl;
  a.download = "Order_Excel_Import.xlsx";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

// 验证Excel文件
const validateExcel = (file) => {
  const isExcel =
    file.type === "application/vnd.ms-excel" ||
    file.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!isExcel) {
    showToast("只能上传Excel文件!");
    return false;
  }
  return true;
};

// 处理Excel上传
const handleExcelUpload = (file) => {
  showLoadingToast({
    message: "正在导入...",
    forbidClick: true,
  });

  const formData = new FormData();
  formData.append("file", file.file);

  fetch("/api/sparts-wb/a/pmc/shopOrder/orderImport", {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((res) => {
      if (res.code === 0) {
        importProductsFromExcel(res.data);
        showSuccessToast("导入成功");
      } else {
        showToast(res.msg || "导入失败");
      }
    })
    .catch(() => {
      showToast("导入失败，请稍后重试");
    })
    .finally(() => {
      showImportSheet.value = false;
    });
};

// 从Excel导入数据处理
const importProductsFromExcel = (data) => {
  data.forEach((item) => {
    if (item.cartList && item.cartList.length > 0) {
      item.cartList.forEach((product) => {
        products.value.push({
          code: product.materielCode || "",
          name: product.materielName || product.materielNameEn || "",
          quantity: product.amount || product.quantity || 1,
          price: product.price || product.unitPrice || 0,
          remark: product.remarks || "",
          expanded: true,
        });
      });
    }
  });
};

// 表单验证
const validateForm = () => {
  // 验证商品信息
  if (products.value.length === 0) {
    showToast("请至少添加一个商品");
    return false;
  }

  for (const product of products.value) {
    if (!product.code) {
      showToast("请输入商品型号/编号");
      return false;
    }
    if (!product.name) {
      showToast("请输入商品名称");
      return false;
    }
    if (!product.quantity || parseFloat(product.quantity) <= 0) {
      showToast("请输入有效的商品数量");
      return false;
    }
    if (!product.price || parseFloat(product.price) <= 0) {
      showToast("请输入有效的商品单价");
      return false;
    }
  }

  // 验证收货信息
  if (isAdmin.value && !selectedDealer.value) {
    showToast("请选择经销商账号");
    return false;
  }

  if (!shipping.name) {
    showToast("请输入收货人姓名");
    return false;
  }

  if (!shipping.phone) {
    showToast("请输入联系电话");
    return false;
  }

  if (!shipping.address) {
    showToast("请输入收货地址");
    return false;
  }

  if (!deliveryDate.value) {
    showToast("请选择交货日期");
    return false;
  }

  if (!paymentDate.value) {
    showToast("请选择付款日期");
    return false;
  }

  return true;
};

// 提交订单
const submitOrder = async () => {
  if (!validateForm()) return;

  submitting.value = true;
  showLoadingToast({
    message: isEdit.value ? "保存中..." : "提交中...",
    forbidClick: true,
  });

  try {
    // 构建订单数据
    const orderData = {
      id: orderId.value,
      remarks: orderRemark.value,
      billingAddress: billing.address,
      deliveryTime: deliveryDate.value,
      payment: paymentDate.value,
      contactEmail: shipping.email,
      contactName: shipping.name,
      contactPhone: shipping.phone,
      collectAddress: shipping.address,
      shopList: products.value.map((product) => ({
        materielCode: product.code,
        materielName: product.name,
        amount: parseFloat(product.quantity),
        unitPrice: parseFloat(product.price),
        remarks: product.remark,
      })),
    };

    // 添加经销商信息（如果是管理员）
    if (isAdmin.value && dealerInfo.value.dealerId) {
      orderData.salesperson = dealerInfo.value.dealerId;
      orderData.customer = dealerInfo.value.customerId;
    }

    // 提交数据
    if (isEdit.value) {
      await apiService.order.saveShopCarOrder(orderData);
    } else {
      await apiService.order.saveShopCarOrder(orderData);
    }

    showSuccessToast(isEdit.value ? "保存成功" : "提交成功");
    // 延迟跳转到订单列表
    router.push("/order");
  } catch (error) {
    console.error("提交订单失败:", error);
    showToast("提交失败，请稍后重试");
  } finally {
    submitting.value = false;
  }
};

// 获取订单详情（编辑模式）
const getOrderDetail = async () => {
  if (!orderId.value) return;

  showLoadingToast({
    message: "加载中...",
    forbidClick: true,
  });

  try {
    const result = await apiService.order.getOrderDetail(orderId.value);
    const data = result.data;

    // 填充订单基本信息
    orderNo.value = data.id || "";
    orderRemark.value = data.remarks || "";
    billing.address = data.billingAddress || "";
    deliveryDate.value = data.deliveryTime || "";
    paymentDate.value = data.payment || "";
    shipping.email = data.contactEmail || "";
    shipping.name = data.contactName || "";
    shipping.phone = data.contactPhone || "";
    shipping.address = data.collectAddress || "";
    createDate.value = data.createDate ? data.createDate.substring(0, 10) : "";

    // 如果是管理员，设置经销商信息
    if (isAdmin.value && data.salesperson) {
      console.log("订单的经销商信息:", {
        salesperson: data.salesperson,
        customer: data.customer,
      });

      dealerInfo.value.dealerId = data.salesperson;
      dealerInfo.value.customerId = data.customer;

      const dealer = dealerList.value.find(
        (item) => item.id === data.salesperson
      );
      console.log("编辑模式找到的经销商:", dealer);

      if (dealer) {
        selectedDealer.value = dealer.name || "";
        const companyInfo = dealer.companyInfo || {};
        selectedCompany.value = companyInfo.name || dealer.companyName || "";
        console.log(
          "设置经销商名称:",
          selectedDealer.value,
          "公司名称:",
          selectedCompany.value
        );
      } else {
        // 没找到对应经销商，可能是API数据结构不一致，做额外的处理
        try {
          // 可能需要根据ID再次查询经销商信息
          apiService.customer.getDealerAccountList().then((result) => {
            const accounts = result?.data || result || [];
            const account = accounts.find((acc) => acc.id === data.salesperson);
            if (account) {
              selectedDealer.value = account.name || "";
              console.log("通过API找到经销商:", selectedDealer.value);

              // 再次查询经销商公司
              apiService.customer.getDealerCompanyList().then((compResult) => {
                const companies = compResult?.data || compResult || [];
                const company = companies.find((c) => c.id === data.customer);
                if (company) {
                  selectedCompany.value = company.name || "";
                  console.log("通过API找到公司:", selectedCompany.value);
                }
              });
            }
          });
        } catch (e) {
          console.error("加载经销商详情失败", e);
        }
      }
    }

    // 填充商品列表
    if (data.productList && data.productList.length > 0) {
      data.productList.forEach((product) => {
        if (
          product.salesOrderItemList &&
          product.salesOrderItemList.length > 0
        ) {
          product.salesOrderItemList.forEach((item) => {
            products.value.push({
              code: item.materielCode || "",
              name: item.materielNameEn || item.materielName || "",
              quantity: item.amount || 1,
              price: item.unitPrice || 0,
              remark: item.remarks || "",
              expanded: true,
            });
          });
        }
      });
    }
  } catch (error) {
    console.error("获取订单详情失败:", error);
    showToast("获取订单详情失败");
  } finally {
    showLoadingToast.clear();
  }
};

// 获取经销商列表
const fetchDealerList = async () => {
  try {
    const result1 = await apiService.customer.getDealerAccountList();
    const result2 = await apiService.customer.getDealerCompanyList();

    const accounts = result1?.data || result1 || [];
    const companies = result2?.data || result2 || [];

    if (accounts.length > 0 && companies.length > 0) {
      dealerList.value = accounts.map((account) => {
        const company = companies.find((c) => c.id === account.customerId);
        return {
          ...account,
          companyInfo: company,
        };
      });
    }
  } catch (error) {
    // 获取经销商列表失败
  }
};

// 初始化用户默认数据
const initUserData = async () => {
  if (userInfo.value.userType === "2") {
    try {
      const data = await apiService.user.getUserProfile();
      if (data && data.data) {
        shipping.address = data.data.street || "";
        billing.address = data.data.street || "";
        shipping.name = data.data.contactName || "";
        shipping.email = data.data.email || "";
        shipping.phone = data.data.phone || "";
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
    }
  }
};

// 初始化页面数据
onMounted(async () => {
  // 初始化默认日期
  const today = new Date();
  deliveryDate.value = formatDate(today);
  paymentDate.value = formatDate(today);
  selectedDeliveryDate.value = today;
  selectedPaymentDate.value = today;

  try {
    // 如果是管理员，获取经销商列表
    if (isAdmin.value) {
      await fetchDealerList();
    }

    // 如果是编辑模式，获取订单详情
    if (isEdit.value) {
      await getOrderDetail();
    } else {
      // 添加默认商品
      addProduct();

      // 初始化用户数据
      await initUserData();
    }
  } catch (error) {
    console.error("页面初始化出错:", error);
  }
});

const formatPriceValue = (value) => {
  return apiService.utils.formatPrice(value);
};
</script>

<style lang="scss" scoped>
.order-create {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;

  .order-nav {
    background-color: #fff;

    &:deep(.van-nav-bar__title) {
      font-weight: bold;
      font-size: 18px;
    }
  }

  .order-container {
    padding: 46px 0 0;
  }

  .order-section {
    margin: 4px 0;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 12px;

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: #323233;
      }
    }

    .product-list {
      padding: 0 4px;
    }

    .product-item {
      margin-bottom: 8px;
    }
  }

  .product-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px;
      border-bottom: 1px solid #f5f5f5;
      cursor: pointer;

      .product-title {
        font-size: 15px;
        font-weight: 500;
        color: #323233;
      }

      .toggle-icon {
        transition: transform 0.3s;
      }
    }

    .product-content {
      padding: 6px 12px;

      .price-quantity {
        display: flex;
        gap: 6px;

        :deep(.van-field) {
          flex: 1;
        }
      }

      .product-subtotal {
        display: flex;
        justify-content: flex-end;
        padding: 6px 0;
        font-size: 14px;

        .price {
          font-weight: bold;
          color: #ee0a24;
          margin-left: 8px;
        }
      }
    }
  }

  .delete-btn {
    height: 100%;
  }

  .order-total {
    margin: 12px;
    padding: 12px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .total-label {
      font-size: 16px;
      font-weight: 500;
    }

    .total-amount {
      font-size: 20px;
      font-weight: bold;
      color: #ee0a24;
    }
  }

  .submit-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 100;
  }

  .import-actions {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    :deep(.van-uploader) {
      width: 100%;

      .van-uploader__wrapper {
        width: 100%;

        .van-uploader__input-wrapper {
          width: 100%;
        }
      }
    }
  }
}

:deep(.van-cell-group--inset) {
  margin: 0 6px;
}

:deep(.van-field) {
  padding: 8px 12px;
}
</style> 