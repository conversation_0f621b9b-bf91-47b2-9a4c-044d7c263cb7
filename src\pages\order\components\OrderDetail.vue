<template>
  <div class="order-detail-page">
    <van-loading v-if="loading" vertical size="24px" class="loading-spinner"
      >加载中...</van-loading
    >

    <template v-else>
      <!-- 订单状态卡片 -->
      <div class="status-card">
        <div class="status-icon">
          <van-icon name="checked" v-if="orderDetail.orderStatus === '1001'" />
          <van-icon
            name="clock-o"
            v-else-if="orderDetail.orderStatus === '1'"
          />
          <van-icon
            name="logistics"
            v-else-if="orderDetail.orderStatus === '20'"
          />
          <van-icon
            name="logistics"
            v-else-if="orderDetail.orderStatus === '30'"
          />
          <van-icon
            name="logistics"
            v-else-if="orderDetail.orderStatus === '1000'"
          />
          <van-icon name="question-o" v-else />
        </div>
        <div class="status-info">
          <div class="status-title">
            <van-tag type="warning" v-if="orderDetail.orderStatus === '1'"
              >待确认</van-tag
            >
            <van-tag type="warning" v-if="orderDetail.orderStatus === '20'"
              >未发货</van-tag
            >
            <van-tag type="danger" v-if="orderDetail.orderStatus === '30'"
              >待取件</van-tag
            >
            <van-tag type="primary" v-if="orderDetail.orderStatus === '1000'"
              >部分发货</van-tag
            >
            <van-tag type="success" v-if="orderDetail.orderStatus === '1001'"
              >已发货</van-tag
            >
          </div>
          <div class="status-desc">
            <span v-if="orderDetail.orderStatus === '1'">等待商家确认</span>
            <span v-else-if="orderDetail.orderStatus === '20'"
              >商家已确认，准备发货中</span
            >
            <span v-else-if="orderDetail.orderStatus === '30'">待配送取件</span>
            <span v-else-if="orderDetail.orderStatus === '1000'"
              >部分商品已发出</span
            >
            <span v-else-if="orderDetail.orderStatus === '1001'">
              已发货，预计{{ estimateDeliveryTime }}送达
            </span>
          </div>
          <div
            class="status-date"
            v-if="
              orderDetail.deliveryTime &&
              (orderDetail.orderStatus === '1000' ||
                orderDetail.orderStatus === '1001')
            "
          >
            发货时间：{{ formatDate(orderDetail.deliveryTime) }}
          </div>
        </div>
      </div>

      <!-- 订单基本信息 -->
      <van-cell-group inset class="info-group">
        <van-cell title="订单号" :value="orderDetail.id" />
        <van-cell
          title="创建时间"
          :value="formatDate(orderDetail.createDate)"
        />
        <van-cell
          title="配送时间"
          :value="formatDate(orderDetail.deliveryTime)"
        />
        <van-cell title="支付日期" :value="formatDate(orderDetail.payment)" />
      </van-cell-group>

      <!-- 联系人信息 -->
      <van-cell-group inset title="联系人信息" class="info-group">
        <van-cell title="联系人" :value="orderDetail.contactName" />
        <van-cell title="联系电话" :value="orderDetail.contactPhone" />
        <van-cell title="联系邮箱" :value="orderDetail.contactEmail" />
      </van-cell-group>

      <!-- 地址信息 -->
      <van-cell-group inset title="地址信息" class="info-group">
        <van-cell title="配送地址">
          <template #value>
            <div class="address-text">{{ orderDetail.collectAddress }}</div>
          </template>
        </van-cell>
        <van-cell title="账单地址">
          <template #value>
            <div class="address-text">{{ orderDetail.billingAddress }}</div>
          </template>
        </van-cell>
      </van-cell-group>


      <!-- 物流信息 -->
      <van-cell-group
        inset
        title="物流信息"
        class="info-group"
        v-if="
          orderDetail.outWarehouseList &&
          orderDetail.outWarehouseList.length > 0
        "
      >
        <div
          v-for="(item, index) in orderDetail.outWarehouseList"
          :key="index"
          class="logistics-item"
        >
          <van-cell :title="`物流 #${index + 1}`">
            <template #label>
              <div class="logistics-info">
                <div class="logistics-company">
                  物流公司: {{ item.logisticsCompany }}
                </div>
                <div class="logistics-number">
                  <span>物流单号: {{ item.logisticsNumber }}</span>
                </div>
                <div class="logistics-actions">
                  <van-button
                    type="primary"
                    size="small"
                    plain
                    icon="down"
                    @click="downloadExcel_packingList(item, orderDetail)"
                    >装箱单</van-button
                  >
                </div>
              </div>
            </template>
          </van-cell>
        </div>
      </van-cell-group>

      <!-- 备注信息 -->
      <van-cell-group
        inset
        title="订单备注"
        class="info-group"
        v-if="orderDetail.remarks"
      >
        <van-cell>
          <template #value>
            <div class="remark-text">{{ orderDetail.remarks }}</div>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 订单商品列表 -->
      <div class="product-list-section">
        <div class="section-header">
          <div class="section-title">商品列表</div>
        </div>

        <!-- 商品列表 - 卡片式布局 -->
        <div class="product-cards">
          <!-- 使用orderDetailList字段 -->
          <div
            v-for="(item, index) in orderDetail.orderDetailList || []"
            :key="'detail-' + index"
            class="product-card"
          >
            <div class="product-card-content">
              <div
                class="product-image"
                @click="previewImage(getImageUrl(item))"
              >
                <van-image :src="getImageUrl(item)[0]" fit="cover" lazy-load>
                  <template #error>
                    <div class="image-placeholder">
                      <van-icon name="photo-o" />
                    </div>
                  </template>
                </van-image>
              </div>
              <div class="product-info">
                <div class="product-header">
                  <span class="product-index">{{ index + 1 }}</span>
                  <span class="product-code">{{ item.materielCode }}</span>
                </div>
                <div class="product-name">
                  {{ item.materielNameEn || item.materielName }}
                </div>
                <div class="product-price-info">
                  <div class="price-item">
                    <span class="price-label">单价:</span>
                    <span class="price-value">
                      {{
                        userInfo.currency === "1"
                          ? "¥"
                          : userInfo.currency === "2"
                          ? "$"
                          : userInfo.currency === "3"
                          ? "€"
                          : ""
                      }}
                      {{ formatPrice(item.unitPrice || item.price) }}
                    </span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">数量:</span>
                    <span class="price-value">{{
                      item.amount || item.quantity || 1
                    }}</span>
                  </div>
                  <div class="price-item total-price">
                    <span class="price-label">小计:</span>
                    <span class="price-value">
                      {{
                        userInfo.currency === "1"
                          ? "¥"
                          : userInfo.currency === "2"
                          ? "$"
                          : userInfo.currency === "3"
                          ? "€"
                          : ""
                      }}
                      {{
                        formatPrice(
                          (item.unitPrice || item.price) *
                            (item.amount || item.quantity || 1)
                        )
                      }}
                    </span>
                  </div>
                </div>
                <div class="product-actions" v-if="item.remarks">
                  <van-button
                    type="primary"
                    size="mini"
                    plain
                    @click="showRemark(item.remarks)"
                    >查看备注</van-button
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 使用salesOrderItemList字段 -->
          <template
            v-if="
              !orderDetail.orderDetailList ||
              orderDetail.orderDetailList.length === 0
            "
          >
            <div
              v-for="(item, index) in orderDetail.salesOrderItemList || []"
              :key="'sales-' + index"
              class="product-card"
            >
              <div class="product-card-content">
                <div
                  class="product-image"
                  @click="previewImage(getImageUrl(item))"
                >
                  <van-image :src="getImageUrl(item)[0]" fit="cover" lazy-load>
                    <template #error>
                      <div class="image-placeholder">
                        <van-icon name="photo-o" />
                      </div>
                    </template>
                  </van-image>
                </div>
                <div class="product-info">
                  <div class="product-header">
                    <span class="product-index">{{ index + 1 }}</span>
                    <span class="product-code">{{ item.materielCode }}</span>
                  </div>
                  <div class="product-name">
                    {{ item.materielNameEn || item.materielName }}
                  </div>
                  <div class="product-price-info">
                    <div class="price-item">
                      <span class="price-label">单价:</span>
                      <span class="price-value">
                        {{
                          userInfo.currency === "1"
                            ? "¥"
                            : userInfo.currency === "2"
                            ? "$"
                            : userInfo.currency === "3"
                            ? "€"
                            : ""
                        }}
                        {{ formatPrice(item.unitPrice || item.price) }}
                      </span>
                    </div>
                    <div class="price-item">
                      <span class="price-label">数量:</span>
                      <span class="price-value">{{
                        item.amount || item.quantity || 1
                      }}</span>
                    </div>
                    <div class="price-item total-price">
                      <span class="price-label">小计:</span>
                      <span class="price-value">
                        {{
                          userInfo.currency === "1"
                            ? "¥"
                            : userInfo.currency === "2"
                            ? "$"
                            : userInfo.currency === "3"
                            ? "€"
                            : ""
                        }}
                        {{
                          formatPrice(
                            (item.unitPrice || item.price) *
                              (item.amount || item.quantity || 1)
                          )
                        }}
                      </span>
                    </div>
                  </div>
                  <div class="product-actions" v-if="item.remarks">
                    <van-button
                      type="primary"
                      size="mini"
                      plain
                      @click="showRemark(item.remarks)"
                      >查看备注</van-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 使用productList字段，可能包含嵌套的cartList -->
          <template
            v-if="
              (!orderDetail.orderDetailList ||
                orderDetail.orderDetailList.length === 0) &&
              (!orderDetail.salesOrderItemList ||
                orderDetail.salesOrderItemList.length === 0) &&
              orderDetail.productList &&
              orderDetail.productList.length > 0
            "
          >
            <template
              v-for="(product, pIndex) in orderDetail.productList"
              :key="'product-' + pIndex"
            >
                              <div class="product-category">
                <div class="product-category-header">
                  <div 
                    class="product-category-image" 
                    @click="previewImage(getImageUrl(null, product))"
                  >
                    <van-image 
                      :src="getImageUrl(null, product).at(-1)" 
                      fit="cover"
                    >
                      <template #error>
                        <div class="image-placeholder">
                          <van-icon name="photo-o" />
                        </div>
                      </template>
                    </van-image>
                  </div>
                  <div class="product-category-title">
                    {{ product.materielCode }}
                    {{ product.materielNameEn || product.materielName }}
                  </div>
                </div>

                <div
                  v-for="(item, index) in product.cartList ||
                  product.salesOrderItemList ||
                  []"
                  :key="'product-item-' + pIndex + '-' + index"
                  class="product-card"
                >
                  <div class="product-card-content">
                    <div class="product-info">
                      <div class="product-header">
                        <span class="product-index"
                          >{{ pIndex + 1 }}.{{ index + 1 }}</span
                        >
                        <span class="product-code">{{
                          item.materielCode
                        }}</span>
                      </div>
                      <div class="product-name">
                        {{ item.materielNameEn || item.materielName }}
                      </div>
                      <div class="product-price-info">
                        <div class="price-item">
                          <span class="price-label">单价:</span>
                          <span class="price-value">
                            {{
                              userInfo.currency === "1"
                                ? "¥"
                                : userInfo.currency === "2"
                                ? "$"
                                : userInfo.currency === "3"
                                ? "€"
                                : ""
                            }}
                            {{ formatPrice(item.unitPrice || item.price) }}
                          </span>
                        </div>
                        <div class="price-item">
                          <span class="price-label">数量:</span>
                          <span class="price-value">{{
                            item.amount || item.quantity || 1
                          }}</span>
                        </div>
                        <div class="price-item total-price">
                          <span class="price-label">小计:</span>
                          <span class="price-value">
                            {{
                              userInfo.currency === "1"
                                ? "¥"
                                : userInfo.currency === "2"
                                ? "$"
                                : userInfo.currency === "3"
                                ? "€"
                                : ""
                            }}
                            {{
                              formatPrice(
                                (item.unitPrice || item.price) *
                                  (item.amount || item.quantity || 1)
                              )
                            }}
                          </span>
                        </div>
                      </div>
                      <div class="product-actions" v-if="item.remarks">
                        <van-button
                          type="primary"
                          size="mini"
                          plain
                          @click="showRemark(item.remarks)"
                          >查看备注</van-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </template>

          <!-- 没有商品时显示提示 -->
          <div
            v-if="
              (!orderDetail.orderDetailList ||
                orderDetail.orderDetailList.length === 0) &&
              (!orderDetail.salesOrderItemList ||
                orderDetail.salesOrderItemList.length === 0) &&
              (!orderDetail.productList || orderDetail.productList.length === 0)
            "
            class="empty-products"
          >
            <van-empty description="暂无商品信息" />
          </div>
        </div>
      </div>

      <!-- 订单操作按钮 -->
      <div class="action-buttons">
        <van-button
          v-if="orderDetail?.orderStatus === '1'"
          type="primary"
          block
          icon="passed"
          @click="confirmOrder"
          >确认订单</van-button
        >

        <van-button
          v-if="isWithin30Min(orderDetail.createDate)"
          type="danger"
          block
          icon="delete"
          @click="showDeleteDialog"
          >删除订单</van-button
        >

        <div class="button-row">
          <van-button
            v-if="
              orderDetail.orderStatus == '1001' ||
              orderDetail.orderStatus == '1000'
            "
            type="warning"
            icon="bill"
            @click="downloadPdf(orderDetail)"
            >下载发票</van-button
          >

          <van-button icon="description" @click="downloadExcel(orderDetail)"
            >导出订单</van-button
          >
        </div>
      </div>
    </template>

    <!-- 确认对话框 -->
    <van-dialog
      v-model:show="confirmDialogVisible"
      title="确认订单"
      show-cancel-button
      @confirm="confirmOrderAction"
    >
      确定要确认此订单吗？
    </van-dialog>

    <!-- 删除对话框 -->
    <van-dialog
      v-model:show="deleteDialogVisible"
      title="删除订单"
      show-cancel-button
      @confirm="deleteOrderAction"
    >
      确定要删除此订单吗？
    </van-dialog>

    <!-- 备注弹窗 -->
    <van-dialog
      v-model:show="remarkDialogVisible"
      title="商品备注"
      show-confirm-button
      confirm-button-text="关闭"
    >
      <div class="remark-dialog-content">
        {{ currentRemark }}
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  inject,
  defineProps,
  defineEmits,
  watch,
  computed,
} from "vue";
import { showToast, showSuccessToast, showImagePreview } from "vant";
import dayjs from "dayjs";
import apiService from "@/utils/api";
import { previewFileForMobile } from "@/utils/mobileDownload";

const props = defineProps({
  params: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["close"]);

// 基础配置
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const userInfo = ref(JSON.parse(localStorage.getItem("userInfo") || "{}"));

// 状态变量
const loading = ref(true);
const orderDetail = ref({});
const confirmDialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const remarkDialogVisible = ref(false);
const currentRemark = ref("");

// 获取订单详情
const getOrderDetail = async () => {
  if (!props.params.id) return;

  loading.value = true;
  try {
    const result = await apiService.order.getOrderDetail(props.params.id);
    orderDetail.value = result.data;
  } catch (error) {
    console.error("获取订单详情失败:", error);
    showToast("获取订单详情失败");
  } finally {
    loading.value = false;
  }
};

// 日期格式化
const formatDate = (date) => {
  if (!date) return "";
  return dayjs(date).format("YYYY-MM-DD");
};

// 预计送达时间
const estimateDeliveryTime = computed(() => {
  if (!orderDetail.value.deliveryTime) return "暂无";
  // 从发货日期算起，预计3-5天送达
  const deliveryDate = dayjs(orderDetail.value.deliveryTime);
  const estimatedMinDate = deliveryDate.add(3, "day").format("MM.DD");
  const estimatedMaxDate = deliveryDate.add(5, "day").format("MM.DD");
  return `${estimatedMinDate}-${estimatedMaxDate}`;
});

// 格式化价格显示
const formatPrice = (value) => {
  return apiService.utils.formatPrice(value);
};

// 检查是否在30分钟内
const isWithin30Min = (dateString) => {
  if (!dateString) return false;
  const currentDate = new Date();
  const givenDate = new Date(dateString);
  const timeDifference = currentDate.getTime() - givenDate.getTime();
  return timeDifference <= 0.5 * 60 * 60 * 1000;
};

// 显示确认对话框
const confirmOrder = () => {
  confirmDialogVisible.value = true;
};

// 显示删除对话框
const showDeleteDialog = () => {
  deleteDialogVisible.value = true;
};

// 显示备注内容
const showRemark = (remark) => {
  currentRemark.value = remark;
  remarkDialogVisible.value = true;
};

// 确认订单
const confirmOrderAction = async () => {
  try {
    await apiService.order.confirmOrder(orderDetail.value.id);
    showSuccessToast("订单确认成功");
    getOrderDetail();
  } catch (error) {
    showToast("确认订单失败");
  }
};

// 删除订单
const deleteOrderAction = async () => {
  try {
    await apiService.order.deleteOrder(orderDetail.value.id);
    showSuccessToast("订单删除成功");
    // 关闭详情页
    emit("close");
  } catch (error) {
    showToast("删除订单失败");
  }
};

// 下载装箱单
const downloadExcel_packingList = (item, order) => {
  apiService.utils.loadDown(
    apiService.urls.orderPackingListExcel +
      item.logisticsNumber +
      "&orderId=" +
      order.id
  );
};

// 下载订单详情
const downloadExcel = (item) => {
  apiService.utils.loadDown(apiService.urls.orderDetailExcel + item.id);
};

// 下载发票PDF
const downloadPdf = (item) => {
  apiService.order
    .generateInvoicePdf({ orderId: item.id })
    .then((res) => {
      if (res.code === "0") {
        const fileUrl = base_downFileByPath_url + res.msg;
        // 使用移动端优化的预览函数
        const success = previewFileForMobile(fileUrl, (error) => {
          showToast("下载被阻止，请允许弹出窗口");
        });

        if (!success) {
          showToast("打开发票失败");
        }
      } else {
        showToast("获取发票失败");
      }
    })
    .catch((error) => {
      showToast("下载失败: " + error.message);
    });
};

// 获取商品图片URL
const getImageUrl = (item, product = null) => {
  let imageUrlList = [];

  // 如果只传入product参数，则只获取设备图片
  if (!item && product && product.productFileList && product.productFileList.length > 0) {
    // 取出product.productFileList里面后缀为图片类型的文件
    const imageFileList = product.productFileList.filter(
      (item) =>
        item.endsWith(".jpg") || item.endsWith(".png") || item.endsWith(".jpeg")
    );
    
    if (imageFileList.length > 0) {
      imageUrlList = imageFileList.map(
        (item) => base_downFileByPath_url + item
      );
      return imageUrlList;
    }
    
    // 如果没有找到图片，返回一个默认图片或空数组
    return ['/assets/images/default-product.png'];
  }
  
  // 从item的productFileList中获取图片
  if (item && item.productFileList && item.productFileList.length > 0) {
    // 处理不同的productFileList结构
    if (typeof item.productFileList[0] === "string") {
      imageUrlList.push(base_downFileByPath_url + item.productFileList[0]);
    } else if (item.productFileList[0] && item.productFileList[0].filePath) {
      imageUrlList.push(
        base_downFileByPath_url + item.productFileList[0].filePath
      );
    }
  }

  // 如果从item中没有找到图片并且有product参数，从product中获取图片
  if (
    imageUrlList.length === 0 && 
    product &&
    product.productFileList &&
    product.productFileList.length > 0
  ) {
    const imageFileList = product.productFileList.filter(
      (item) =>
        item.endsWith(".jpg") || item.endsWith(".png") || item.endsWith(".jpeg")
    );
    
    if (imageFileList.length > 0) {
      imageUrlList = imageFileList.map(
        (item) => base_downFileByPath_url + item
      );
    }
  }
  
  return imageUrlList.length > 0 ? imageUrlList : ['/assets/images/default-product.png'];
};

// 预览图片
const previewImage = (imageUrl) => {
  if (!imageUrl) return;
  showImagePreview(imageUrl);
};

// 监听参数变化
watch(
  () => props.params.id,
  (newVal) => {
    if (newVal) {
      getOrderDetail();
    }
  },
  { immediate: true }
);

// 页面加载时获取数据
onMounted(() => {
  getOrderDetail();
});
</script>

<style lang="scss" scoped>
.order-detail-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 8px;
  padding-top: 6px;

  .loading-spinner {
    margin-top: 100px;
  }

  .status-card {
    display: flex;
    padding: 10px 12px;
    background-color: #fff;
    margin: 8px;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-top: 0;
    align-items: center;

    .status-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #f0f9eb;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      .van-icon {
        font-size: 16px;
        color: #07c160;
      }
    }

    .status-info {
      flex: 1;

      .status-title {
        margin-bottom: 4px;
      }

      .status-desc {
        color: #666;
        font-size: 12px;
        margin-bottom: 3px;
      }

      .status-date {
        font-size: 11px;
        color: #969799;
      }
    }
  }
  :deep(.van-cell-group__title) {
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .info-group {
    margin: 6px 8px;
    border-radius: 6px;
    overflow: hidden;
  }

  .address-text {
    word-break: break-all;
    color: #666;
    font-size: 12px;
  }

  .price-label {
    font-size: 13px;
    color: #323233;
  }

  .price-value {
    font-size: 14px;
    font-weight: bold;
    color: #ee0a24;
  }

  .logistics-item {
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .logistics-info {
      padding: 6px 0;

      .logistics-company,
      .logistics-number {
        margin-bottom: 6px;
        font-size: 12px;
        color: #666;
      }

      .logistics-actions {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .remark-text {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }

  .product-list-section {
    margin: 6px 8px;
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .section-header {
      padding: 8px 12px;
      border-bottom: 1px solid #f5f5f5;

      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: #323233;
      }
    }

    .product-cards {
      padding: 4px;
    }

        .product-card {
      margin-bottom: 8px;
      border: 1px solid #ebedf0;
      border-radius: 6px;
      overflow: hidden;
      background-color: #fff;

      &:last-child {
        margin-bottom: 0;
      }

      .product-card-content {
        display: flex;

        .product-image {
          width: 70px;
          height: 70px;
          flex-shrink: 0;
          position: relative;
          overflow: hidden;

          .van-image {
            width: 100%;
            height: 100%;
          }

          .image-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f7f8fa;
            display: flex;
            align-items: center;
            justify-content: center;

            .van-icon {
              font-size: 20px;
              color: #dcdee0;
            }
          }
        }

        .product-info {
          flex: 1;
          padding: 6px 8px;
          position: relative;

          .product-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;

            .product-index {
              font-size: 11px;
              color: #969799;
              background-color: #f2f3f5;
              padding: 0px 4px;
              border-radius: 8px;
            }

            .product-code {
              font-size: 11px;
              color: #323233;
            }
          }

          .product-name {
            font-size: 13px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .product-price-info {
            display: flex;
            flex-wrap: wrap;
            font-size: 11px;

            .price-item {
              margin-right: 8px;
              margin-bottom: 2px;

              .price-label {
                color: #969799;
                font-size: 11px;
                font-weight: normal;
              }

              .price-value {
                color: #323233;
                font-size: 11px;
                font-weight: normal;
                margin-left: 2px;
              }

              &.total-price {
                .price-label {
                  color: #ee0a24;
                }

                .price-value {
                  color: #ee0a24;
                  font-weight: 500;
                }
              }
            }
          }

          .product-actions {
            margin-top: 4px;
          }
        }
      }
    }

    .product-category {
      margin-bottom: 10px;
      
      // 产品分类下的商品卡片（配件卡片）样式
      .product-card {
        margin-left: 10px;
        margin-right: 2px;
        margin-bottom: 6px;
      }

      .product-category-header {
        display: flex;
        align-items: center;
        background-color: #f2f3f5;
        border-radius: 4px;
        padding: 6px 8px;
        margin-bottom: 4px;
        
        .product-category-image {
          width: 48px;
          height: 48px;
          flex-shrink: 0;
          margin-right: 8px;
          overflow: hidden;
          border-radius: 3px;
          
          .van-image {
            width: 100%;
            height: 100%;
          }
          
          .image-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f7f8fa;
            display: flex;
            align-items: center;
            justify-content: center;
            
            .van-icon {
              font-size: 20px;
              color: #dcdee0;
            }
          }
        }
        
        .product-category-title {
          font-size: 12px;
          font-weight: 500;
          color: #323233;
          flex: 1;
        }
      }
    }

    .empty-products {
      padding: 24px 0;
    }
  }

  .action-buttons {
    margin: 12px 8px 16px;

    .van-button {
      margin-bottom: 8px;
    }

    .button-row {
      display: flex;
      gap: 8px;

      .van-button {
        flex: 1;
      }
    }
  }

  .remark-dialog-content {
    padding: 12px;
    font-size: 13px;
    color: #323233;
    line-height: 1.4;
    max-height: 240px;
    overflow-y: auto;
  }
}
</style> 
